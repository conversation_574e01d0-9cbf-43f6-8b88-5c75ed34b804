{"name": "xrpl", "version": "4.3.0", "license": "ISC", "description": "A TypeScript/JavaScript API for interacting with the XRP Ledger in Node.js and the browser", "files": ["build/xrpl-latest-min.js", "build/xrpl-latest-min.js.map", "build/xrpl-latest.js", "build/xrpl-latest.js.map", "dist/npm/*", "src/*"], "main": "dist/npm/", "unpkg": "build/xrpl-latest-min.js", "jsdelivr": "build/xrpl-latest-min.js", "types": "dist/npm/index.d.ts", "directories": {"test": "test"}, "browser": {"ws": "./dist/npm/client/WSWrapper.js"}, "dependencies": {"@scure/bip32": "^1.3.1", "@scure/bip39": "^1.2.1", "@xrplf/isomorphic": "^1.0.1", "@xrplf/secret-numbers": "^1.0.0", "bignumber.js": "^9.0.0", "eventemitter3": "^5.0.1", "ripple-address-codec": "^5.0.0", "ripple-binary-codec": "^2.4.0", "ripple-keypairs": "^2.0.0"}, "devDependencies": {"@types/node": "^18.18.38", "eventemitter3": "^5.0.1", "https-proxy-agent": "^7.0.1", "karma": "^6.4.1", "karma-chrome-launcher": "^3.1.1", "karma-jasmine": "^5.1.0", "karma-webpack": "^5.0.0", "lodash": "^4.17.4", "react": "^19.0.0", "run-s": "^0.0.0", "typedoc": "0.28.5", "ws": "^8.14.2"}, "scripts": {"build": "run-s build:lib build:snippets build:web", "build:snippets": "tsc --build ./snippets/tsconfig.json", "build:lib": "tsc --build tsconfig.build.json", "build:web": "webpack", "build:browserTests": "webpack --config ./test/webpack.config.js", "analyze": "webpack --analyze", "watch": "run-s build:lib --watch", "clean": "rm -rf ./dist ./coverage ./test/testCompiledForWeb tsconfig.build.tsbuildinfo", "docgen": "tsc --build tsconfig.docs.json && typedoc && echo js.xrpl.org >> ../../docs/CNAME", "prepare": "copyfiles ../../README.md xrpl/README.md", "prepublish": "run-s clean build", "test": "jest --config=jest.config.unit.js --verbose false --silent=false", "test:integration": "TS_NODE_PROJECT=tsconfig.build.json jest --config=jest.config.integration.js --verbose false --silent=false --runInBand", "test:browser": "npm run build && npm run build:browserTests && karma start ./karma.config.js", "test:watch": "jest --watch --config=jest.config.unit.js --verbose false --silent=false", "format": "prettier --write '{src,test}/**/*.ts'", "lint": "eslint . --ext .ts --max-warnings 0", "perf": "./scripts/perf_test.sh", "compile:snippets": "tsc -p snippets/tsconfig.json", "start:snippet": "npm run compile:snippets && node", "inspect:snippet": "npm run compile:snippets && node inspect"}, "prettier": "@xrplf/prettier-config", "repository": {"type": "git", "url": "**************:XRPLF/xrpl.js.git"}, "readmeFilename": "README.md", "keywords": ["ripple-lib", "ripple", "xrp", "xrp ledger", "xrpl"], "engines": {"node": ">=18.0.0"}, "gitHead": "3c19700c64d57a344655081b0bbc6b65bd3044f5"}